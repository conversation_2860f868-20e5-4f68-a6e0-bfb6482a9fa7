#!/bin/bash

# Simple Cloud Buildpacks deployment for FastAPI
# This script uses the --source flag with buildpacks detection

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
SERVICE_NAME="fastapi-sample-app"
REGION="us-central1"

echo "🚀 Deploying FastAPI app with Cloud Buildpacks (Simple Method)..."
echo "Project ID: $PROJECT_ID"
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first:"
    echo "https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Set the project
echo "📋 Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# Ensure no Dockerfile exists (so buildpacks are used)
if [ -f "Dockerfile" ]; then
    echo "📦 Moving Dockerfile temporarily to use buildpacks..."
    mv Dockerfile Dockerfile.temp
fi

# Deploy using buildpacks with --source flag
echo "🏗️  Building and deploying with Cloud Buildpacks..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --concurrency 80 \
    --timeout 300 \
    --cpu-throttling \
    --execution-environment gen2 \
    --set-env-vars "PYTHONUNBUFFERED=1"

# Restore Dockerfile if it was moved
if [ -f "Dockerfile.temp" ]; then
    echo "📦 Restoring Dockerfile..."
    mv Dockerfile.temp Dockerfile
fi

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

echo "✅ Deployment complete!"
echo "🌐 Service URL: $SERVICE_URL"
echo "📚 API Documentation: $SERVICE_URL/docs"
echo "🔍 Health Check: $SERVICE_URL/health"
echo ""
echo "🎉 Your FastAPI app is now running on Cloud Run with Cloud Buildpacks!"
echo "💰 Configured for Google Cloud Free Tier - scales to zero when not in use."
