# Cloud Buildpacks Deployment Guide

This guide shows how to deploy your FastAPI application using **Google Cloud Buildpacks** instead of a Dockerfile. Buildpacks automatically detect your application and create optimized container images.

## Why Use Cloud Buildpacks?

### Advantages
- ✅ **No Dockerfile needed** - Automatic detection and optimization
- ✅ **Smaller images** - Only includes necessary dependencies
- ✅ **Security updates** - Automatically updated base images
- ✅ **Best practices** - Google's recommended configurations
- ✅ **Faster builds** - Intelligent caching and layer reuse
- ✅ **Free tier friendly** - Optimized resource usage

### Buildpacks vs Dockerfile
| Feature | Buildpacks | Dockerfile |
|---------|------------|------------|
| Setup complexity | Minimal | Manual |
| Image size | Optimized | Depends on config |
| Security updates | Automatic | Manual |
| Build speed | Fast (caching) | Varies |
| Customization | Limited | Full control |

## Files for Buildpacks

### Required Files
- `main.py` - Your FastAPI application
- `Pipfile` & `Pipfile.lock` - Python dependencies

### Optional Configuration
- `project.toml` - Buildpack configuration
- `.gcloudignore` - Files to exclude from build

## Deployment Methods

### Method 1: Quick Deploy (Recommended)
```bash
# Deploy with buildpacks script
./deploy-buildpacks.sh your-gcp-project-id
```

### Method 2: Manual gcloud Command
```bash
# Deploy directly from source
gcloud run deploy fastapi-sample-app \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --concurrency 80 \
    --timeout 300 \
    --cpu-throttling \
    --execution-environment gen2
```

### Method 3: Two-Step Process
```bash
# Step 1: Build with buildpacks
gcloud builds submit --pack image=gcr.io/PROJECT_ID/fastapi-sample-app

# Step 2: Deploy the built image
gcloud run deploy fastapi-sample-app \
    --image gcr.io/PROJECT_ID/fastapi-sample-app \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated
```

## Buildpack Detection

Cloud Buildpacks automatically detects your application type:

1. **Python Detection**: Looks for `requirements.txt`, `Pipfile`, or `setup.py`
2. **Web Framework**: Detects FastAPI/Flask/Django automatically
3. **Entry Point**: Uses `main.py` or detects from Pipfile scripts

## Configuration Options

### project.toml Configuration
```toml
[build]
builder = "gcr.io/buildpacks/builder:v1"

[[build.env]]
name = "PORT"
value = "8000"

[[build.env]]
name = "PYTHONUNBUFFERED"
value = "1"
```

### Environment Variables
Set during deployment:
```bash
--set-env-vars "PORT=8000,PYTHONUNBUFFERED=1,DEBUG=false"
```

## Free Tier Optimization

### Buildpack Benefits for Free Tier
- **Smaller images** = Faster cold starts
- **Optimized dependencies** = Lower memory usage
- **Automatic caching** = Faster subsequent builds
- **Security patches** = No maintenance overhead

### Resource Configuration
```bash
--memory 512Mi          # Free tier friendly
--cpu 1                 # Single CPU
--min-instances 0       # Scale to zero
--max-instances 10      # Reasonable limit
--concurrency 80        # Efficient request handling
```

## Troubleshooting

### Build Issues
```bash
# Check build logs
gcloud builds log $(gcloud builds list --limit=1 --format="value(id)")

# Verbose buildpack output
gcloud builds submit --pack image=gcr.io/PROJECT_ID/app --verbosity=debug
```

### Runtime Issues
```bash
# Check service logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50

# Describe service
gcloud run services describe fastapi-sample-app --region=us-central1
```

### Common Solutions

**Build fails to detect Python:**
```bash
# Ensure Pipfile exists and is valid
pipenv check
```

**Port binding issues:**
```bash
# Verify PORT environment variable
gcloud run services update fastapi-sample-app \
    --set-env-vars "PORT=8080" \
    --region us-central1
```

**Memory issues:**
```bash
# Increase memory (still free tier)
gcloud run services update fastapi-sample-app \
    --memory 1Gi \
    --region us-central1
```

## Comparison: Buildpacks vs Dockerfile

### When to Use Buildpacks
- ✅ Standard Python web applications
- ✅ Want automatic optimization
- ✅ Prefer minimal configuration
- ✅ Need automatic security updates
- ✅ Free tier optimization

### When to Use Dockerfile
- ✅ Need custom system dependencies
- ✅ Complex multi-stage builds
- ✅ Specific base image requirements
- ✅ Custom optimization needs
- ✅ Full control over build process

## Migration from Dockerfile

If you want to switch back to Dockerfile:
1. Keep your existing `Dockerfile`
2. Use `deploy.sh` instead of `deploy-buildpacks.sh`
3. Remove `project.toml` (optional)

## Best Practices

1. **Keep Pipfile.lock updated**: `pipenv lock`
2. **Use .gcloudignore**: Exclude unnecessary files
3. **Monitor builds**: Check build times and image sizes
4. **Test locally**: Use `pipenv run python main.py`
5. **Set up monitoring**: Use Cloud Monitoring for insights

## Next Steps

1. Deploy with buildpacks: `./deploy-buildpacks.sh your-project-id`
2. Test your API: Visit the provided URL + `/docs`
3. Monitor usage: Set up billing alerts
4. Scale as needed: Adjust memory/CPU if required

Buildpacks provide an excellent balance of simplicity and optimization, making them perfect for FastAPI applications on the free tier!
