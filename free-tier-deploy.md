# Google Cloud Run Free Tier Deployment Guide

This guide helps you deploy the FastAPI application to Google Cloud Run while staying within the **Always Free** tier limits.

## Free Tier Limits (as of 2024)

Google Cloud Run Always Free tier includes:
- **2 million requests per month**
- **400,000 GB-seconds of memory**
- **200,000 vCPU-seconds**
- **1 GB network egress per month**

## Optimized Configuration

Our configuration is designed to maximize free tier usage:

### Resource Allocation
- **Memory**: 512MB (well below 2GB limit)
- **CPU**: 1 vCPU with throttling enabled
- **Min instances**: 0 (scales to zero when not in use)
- **Max instances**: 10 (reasonable limit)
- **Concurrency**: 80 requests per instance

### Cost Optimization Features
- **Scale to zero**: No charges when idle
- **CPU throttling**: Reduces CPU usage when possible
- **Gen2 execution environment**: More efficient
- **Single worker**: Minimizes memory usage
- **Request limits**: Prevents runaway costs

## Deployment Steps

### 1. Prerequisites
```bash
# Install Google Cloud CLI
# https://cloud.google.com/sdk/docs/install

# Authenticate
gcloud auth login

# Set your project (replace with your project ID)
gcloud config set project YOUR_PROJECT_ID
```

### 2. Enable Required APIs
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
```

### 3. Deploy Using Script
```bash
# Make script executable
chmod +x deploy.sh

# Deploy (replace with your project ID)
./deploy.sh YOUR_PROJECT_ID
```

### 4. Alternative Manual Deployment
```bash
# Build image
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/fastapi-sample-app

# Deploy with free tier settings
gcloud run deploy fastapi-sample-app \
    --image gcr.io/YOUR_PROJECT_ID/fastapi-sample-app \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --concurrency 80 \
    --timeout 300 \
    --cpu-throttling \
    --execution-environment gen2
```

## Monitoring Free Tier Usage

### Check Current Usage
```bash
# View service details
gcloud run services describe fastapi-sample-app --region=us-central1

# Check logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50
```

### Monitor in Console
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Navigate to **Billing** → **Budgets & alerts**
3. Set up budget alerts for $0.01 to get notified of any charges

## Free Tier Best Practices

### 1. Scale to Zero
- Service automatically scales to 0 when not in use
- No charges during idle periods
- Cold starts may take 1-3 seconds

### 2. Optimize Requests
- Use connection pooling
- Implement caching where possible
- Batch operations when feasible

### 3. Monitor Usage
- Set up billing alerts
- Review usage monthly
- Use Cloud Monitoring for insights

### 4. Regional Selection
- Use `us-central1`, `us-east1`, or `us-west1` for free tier
- These regions have the most generous free tier allocations

## Expected Monthly Costs

With our configuration and typical usage:
- **Light usage** (< 100 requests/day): **$0.00**
- **Moderate usage** (< 1000 requests/day): **$0.00**
- **Heavy usage** (approaching limits): **$0.00 - $1.00**

## Troubleshooting

### Cold Starts
If you experience slow initial responses:
```bash
# Set minimum instances to 1 (will incur small cost)
gcloud run services update fastapi-sample-app \
    --min-instances 1 \
    --region us-central1
```

### Memory Issues
If you encounter memory errors:
```bash
# Increase memory (still within free tier)
gcloud run services update fastapi-sample-app \
    --memory 1Gi \
    --region us-central1
```

### Timeout Issues
For longer-running requests:
```bash
# Increase timeout (max 3600 seconds)
gcloud run services update fastapi-sample-app \
    --timeout 900 \
    --region us-central1
```

## Cleanup

To avoid any potential charges:
```bash
# Delete the service
gcloud run services delete fastapi-sample-app --region=us-central1

# Delete container images
gcloud container images delete gcr.io/YOUR_PROJECT_ID/fastapi-sample-app
```

## Additional Free Tier Services

Consider these other free GCP services for your application:
- **Cloud Storage**: 5GB free
- **Cloud Firestore**: 1GB storage + 50K reads/day
- **Cloud Functions**: 2M invocations/month
- **Cloud Build**: 120 build-minutes/day
