# Cloud Buildpacks configuration for FastAPI
# This file configures how Google Cloud Buildpacks builds your application

[build]
# Include only necessary files
include = [
    "main.py",
    "requirements.txt"
]

# Exclude unnecessary files for smaller image
exclude = [
    "README.md",
    "*.md",
    ".git/",
    ".gitignore",
    "__pycache__/",
    "*.pyc",
    ".pytest_cache/",
    "venv/",
    "env/",
    ".env"
]

# Environment variables for the built image
[[build.env]]
name = "PYTHONUNBUFFERED"
value = "1"

[[build.env]]
name = "PYTHONDONTWRITEBYTECODE"
value = "1"
