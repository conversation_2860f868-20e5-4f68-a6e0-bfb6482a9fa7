# Cloud Buildpacks configuration for FastAPI
# This file configures how Google Cloud Buildpacks builds your application

[build]
# Use Google Cloud Buildpacks for Python
builder = "gcr.io/buildpacks/builder:v1"

# Include only necessary files
include = [
    "main.py",
    "Pipfile",
    "Pipfile.lock"
]

# Exclude unnecessary files for smaller image
exclude = [
    "README.md",
    "*.md",
    ".git/",
    ".gitignore",
    "__pycache__/",
    "*.pyc",
    ".pytest_cache/",
    "venv/",
    "env/",
    ".env",
    "Dockerfile",
    ".dockerignore"
]

[[build.env]]
name = "GOOGLE_FUNCTION_SIGNATURE_TYPE"
value = "http"

[[build.env]]
name = "GOOGLE_FUNCTION_TARGET"
value = "app"

# Python-specific buildpack configuration
[build.buildpacks]
uri = "gcr.io/buildpacks/python"

# Environment variables for the built image
[[build.env]]
name = "PYTHONUNBUFFERED"
value = "1"

[[build.env]]
name = "PYTHONDONTWRITEBYTECODE"
value = "1"
