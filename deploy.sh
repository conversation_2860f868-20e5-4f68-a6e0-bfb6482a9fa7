#!/bin/bash

# Deploy FastAPI app to Google Cloud Run using Cloud Buildpacks
# Optimized for Google Cloud Free Tier

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
SERVICE_NAME="fastapi-sample-app"
REGION="us-central1"

echo "🚀 Deploying FastAPI app to Google Cloud Run..."
echo "Project ID: $PROJECT_ID"
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"

# Validation
if [ "$PROJECT_ID" = "your-gcp-project-id" ]; then
    echo "❌ Please provide your actual GCP project ID:"
    echo "Usage: ./deploy.sh YOUR_PROJECT_ID"
    exit 1
fi

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first:"
    echo "https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Set the project
echo "📋 Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# Deploy using Cloud Buildpacks
echo "🏗️  Building and deploying with Cloud Buildpacks..."
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --concurrency 80 \
    --timeout 300 \
    --cpu-throttling \
    --execution-environment gen2 \
    --set-env-vars "PYTHONUNBUFFERED=1"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

echo ""
echo "✅ Deployment complete!"
echo "🌐 Service URL: $SERVICE_URL"
echo "📚 API Documentation: $SERVICE_URL/docs"
echo "🔍 Health Check: $SERVICE_URL/health"
echo ""
echo "💰 Free Tier Optimized:"
echo "  • Scales to zero when not in use"
echo "  • 512MB memory (within free limits)"
echo "  • CPU throttling enabled"
echo "  • Up to 2M requests/month free"
