from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import uuid
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Sample FastAPI Application",
    description="A sample FastAPI application deployed on Google Cloud Platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class Item(BaseModel):
    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    price: float
    category: str
    created_at: Optional[datetime] = None

class ItemCreate(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    category: str

class ItemUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    category: Optional[str] = None

# In-memory storage (for demo purposes)
items_db = {}

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to the Sample FastAPI Application!",
        "docs": "/docs",
        "health": "/health"
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "FastAPI Sample App"
    }

# Get all items with optional filtering
@app.get("/items/", response_model=List[Item])
async def get_items(
    category: Optional[str] = Query(None, description="Filter by category"),
    min_price: Optional[float] = Query(None, description="Minimum price filter"),
    max_price: Optional[float] = Query(None, description="Maximum price filter"),
    limit: int = Query(10, description="Number of items to return")
):
    filtered_items = list(items_db.values())
    
    if category:
        filtered_items = [item for item in filtered_items if item["category"].lower() == category.lower()]
    
    if min_price is not None:
        filtered_items = [item for item in filtered_items if item["price"] >= min_price]
    
    if max_price is not None:
        filtered_items = [item for item in filtered_items if item["price"] <= max_price]
    
    return filtered_items[:limit]

# Get single item by ID
@app.get("/items/{item_id}", response_model=Item)
async def get_item(item_id: str):
    if item_id not in items_db:
        raise HTTPException(status_code=404, detail="Item not found")
    return items_db[item_id]

# Create new item
@app.post("/items/", response_model=Item)
async def create_item(item: ItemCreate):
    item_id = str(uuid.uuid4())
    new_item = {
        "id": item_id,
        "name": item.name,
        "description": item.description,
        "price": item.price,
        "category": item.category,
        "created_at": datetime.now()
    }
    items_db[item_id] = new_item
    return new_item

# Update existing item
@app.put("/items/{item_id}", response_model=Item)
async def update_item(item_id: str, item_update: ItemUpdate):
    if item_id not in items_db:
        raise HTTPException(status_code=404, detail="Item not found")
    
    stored_item = items_db[item_id]
    update_data = item_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        stored_item[field] = value
    
    items_db[item_id] = stored_item
    return stored_item

# Delete item
@app.delete("/items/{item_id}")
async def delete_item(item_id: str):
    if item_id not in items_db:
        raise HTTPException(status_code=404, detail="Item not found")
    
    deleted_item = items_db.pop(item_id)
    return {"message": f"Item '{deleted_item['name']}' deleted successfully"}

# Get categories
@app.get("/categories/")
async def get_categories():
    categories = list(set(item["category"] for item in items_db.values()))
    return {"categories": categories}

# Search items
@app.get("/search/")
async def search_items(q: str = Query(..., description="Search query")):
    results = []
    query_lower = q.lower()
    
    for item in items_db.values():
        if (query_lower in item["name"].lower() or 
            (item["description"] and query_lower in item["description"].lower()) or
            query_lower in item["category"].lower()):
            results.append(item)
    
    return {"query": q, "results": results, "count": len(results)}

# Initialize with some sample data
@app.on_event("startup")
async def startup_event():
    sample_items = [
        {
            "id": str(uuid.uuid4()),
            "name": "Laptop",
            "description": "High-performance laptop for development",
            "price": 1299.99,
            "category": "Electronics",
            "created_at": datetime.now()
        },
        {
            "id": str(uuid.uuid4()),
            "name": "Coffee Mug",
            "description": "Ceramic coffee mug with handle",
            "price": 12.99,
            "category": "Kitchen",
            "created_at": datetime.now()
        },
        {
            "id": str(uuid.uuid4()),
            "name": "Python Book",
            "description": "Learn Python programming",
            "price": 39.99,
            "category": "Books",
            "created_at": datetime.now()
        }
    ]
    
    for item in sample_items:
        items_db[item["id"]] = item

# Export the app for Vercel
handler = app
