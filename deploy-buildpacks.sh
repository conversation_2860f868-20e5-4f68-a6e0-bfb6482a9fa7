#!/bin/bash

# Deploy FastAPI app to Google Cloud Run using Cloud Buildpacks
# This script uses buildpacks instead of Dockerfile for automatic optimization

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
SERVICE_NAME="fastapi-sample-app"
REGION="us-central1"
SOURCE_DIR="."

echo "🚀 Deploying FastAPI app to Google Cloud Run using Cloud Buildpacks..."
echo "Project ID: $PROJECT_ID"
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first:"
    echo "https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Not authenticated with gcloud. Please run: gcloud auth login"
    exit 1
fi

# Set the project
echo "📋 Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# Build with buildpacks first, then deploy
echo "🏗️  Building with Cloud Buildpacks..."
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

# Use buildpacks to build the image
gcloud builds submit \
    --pack image=$IMAGE_NAME \
    --region=$REGION

echo "🚀 Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --concurrency 80 \
    --timeout 300 \
    --cpu-throttling \
    --execution-environment gen2 \
    --port 8000 \
    --set-env-vars "PYTHONUNBUFFERED=1"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

echo "✅ Deployment complete!"
echo "🌐 Service URL: $SERVICE_URL"
echo "📚 API Documentation: $SERVICE_URL/docs"
echo "🔍 Health Check: $SERVICE_URL/health"
echo ""
echo "🎉 Your FastAPI app is now running on Cloud Run with automatic buildpack optimization!"
echo "💰 Configured for Google Cloud Free Tier - scales to zero when not in use."
