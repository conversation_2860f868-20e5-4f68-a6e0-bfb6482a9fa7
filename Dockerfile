# Use Python 3.12 slim image for smaller size and faster builds
FROM python:3.12-slim

# Set environment variables for optimization
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8000
ENV PYTHONPATH=/app

# Set work directory
WORKDIR /app

# Install system dependencies (minimal for free tier)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install pipenv
RUN pip install --no-cache-dir pipenv

# Copy dependency files first for better caching
COPY Pipfile Pipfile.lock ./

# Install Python dependencies (system-wide for smaller image)
RUN pipenv install --system --deploy --clear

# Copy project files
COPY . .

# Create non-root user for security
RUN adduser --disabled-password --gecos '' --uid 1000 appuser \
    && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE $PORT

# Health check for Cloud Run
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# Run with optimized settings for free tier
# Single worker to minimize memory usage
CMD exec gunicorn \
    --bind 0.0.0.0:$PORT \
    --workers 1 \
    --worker-class uvicorn.workers.UvicornWorker \
    --worker-connections 1000 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --timeout 300 \
    --keep-alive 2 \
    --preload \
    main:app
