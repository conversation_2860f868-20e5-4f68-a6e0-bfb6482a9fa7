# Sample FastAPI Application

A sample FastAPI application with CRUD operations, deployed on Google Cloud Platform.

## Features

- **RESTful API** with full CRUD operations
- **Interactive API Documentation** (Swagger UI)
- **Data Validation** using Pydantic models
- **CORS Support** for web applications
- **Search and Filtering** capabilities
- **Health Check** endpoint
- **Sample Data** pre-loaded

## API Endpoints

### Core Endpoints
- `GET /` - Welcome message and API info
- `GET /health` - Health check
- `GET /docs` - Interactive API documentation (Swagger UI)
- `GET /redoc` - Alternative API documentation

### Items Management
- `GET /items/` - Get all items (with optional filtering)
  - Query parameters: `category`, `min_price`, `max_price`, `limit`
- `GET /items/{item_id}` - Get specific item by ID
- `POST /items/` - Create new item
- `PUT /items/{item_id}` - Update existing item
- `DELETE /items/{item_id}` - Delete item

### Additional Features
- `GET /categories/` - Get all available categories
- `GET /search/?q={query}` - Search items by name, description, or category

## Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   python main.py
   # or
   uvicorn main:app --reload
   ```

3. **Access the API:**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

## Deployment to Google Cloud Platform

### Prerequisites
- [Google Cloud CLI](https://cloud.google.com/sdk/docs/install) installed
- Google Cloud Platform account with billing enabled
- A GCP project created

### Option 1: Cloud Run (Recommended - Free Tier Optimized)

Cloud Run is serverless, scales to zero, and perfect for FastAPI applications. **This configuration is optimized to stay within Google Cloud's Always Free tier.**

#### Free Tier Benefits
- **2 million requests/month** - More than enough for most applications
- **Scales to zero** - No charges when idle
- **512MB memory** - Efficient resource usage
- **CPU throttling** - Reduces costs when possible

#### Quick Deploy Options

**Option A: Cloud Buildpacks (Recommended)**
```bash
# Automatic optimization, no Dockerfile needed
./deploy-buildpacks.sh your-gcp-project-id
```

**Option B: Docker Build**
```bash
# Traditional Docker build approach
./deploy.sh your-gcp-project-id
```

📖 **See [buildpacks-guide.md](buildpacks-guide.md) for Cloud Buildpacks details**
📖 **See [free-tier-deploy.md](free-tier-deploy.md) for detailed free tier optimization guide**

#### Manual Deploy Steps

1. **Authenticate with Google Cloud:**
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

2. **Enable required APIs:**
   ```bash
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable run.googleapis.com
   ```

3. **Build and deploy:**
   ```bash
   # Build the container image
   gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/fastapi-sample-app

   # Deploy to Cloud Run
   gcloud run deploy fastapi-sample-app \
     --image gcr.io/YOUR_PROJECT_ID/fastapi-sample-app \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

### Option 2: App Engine

App Engine provides a fully managed platform with automatic scaling.

1. **Deploy to App Engine:**
   ```bash
   gcloud app deploy app.yaml
   ```

2. **View your application:**
   ```bash
   gcloud app browse
   ```

### Environment Variables (if needed)
For Cloud Run, set environment variables during deployment:
```bash
gcloud run deploy fastapi-sample-app \
  --set-env-vars "ENV_VAR_NAME=value"
```

For App Engine, add them to `app.yaml`:
```yaml
env_variables:
  ENV_VAR_NAME: "value"
```

## Project Structure

```
.
├── main.py              # Main FastAPI application
├── Pipfile              # Python dependencies (pipenv)
├── Pipfile.lock         # Locked dependencies
├── Dockerfile           # Container configuration for Cloud Run
├── .dockerignore        # Docker ignore file
├── project.toml         # Cloud Buildpacks configuration
├── .gcloudignore        # Files to exclude from Cloud Build
├── service.yaml         # Cloud Run service configuration
├── deploy.sh            # Deployment script (Docker)
├── deploy-buildpacks.sh # Deployment script (Buildpacks)
└── README.md            # This file
```

## Sample Data

The application comes pre-loaded with sample items:
- Laptop (Electronics)
- Coffee Mug (Kitchen)
- Python Book (Books)

## API Usage Examples

### Create a new item:
```bash
curl -X POST "https://your-cloud-run-url/items/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Wireless Mouse",
    "description": "Ergonomic wireless mouse",
    "price": 29.99,
    "category": "Electronics"
  }'
```

### Get items with filtering:
```bash
curl "https://your-cloud-run-url/items/?category=Electronics&min_price=20"
```

### Search items:
```bash
curl "https://your-cloud-run-url/search/?q=laptop"
```

## Technologies Used

- **FastAPI** - Modern, fast web framework for building APIs
- **Pydantic** - Data validation using Python type annotations
- **Uvicorn** - ASGI server implementation
- **Gunicorn** - Python WSGI HTTP Server for production
- **Google Cloud Run** - Serverless container platform
- **Docker** - Containerization platform

## License

This is a sample application for demonstration purposes.
