# Sample FastAPI Application

A sample FastAPI application with CRUD operations, deployed on Google Cloud Platform.

## Features

- **RESTful API** with full CRUD operations
- **Interactive API Documentation** (Swagger UI)
- **Data Validation** using Pydantic models
- **CORS Support** for web applications
- **Search and Filtering** capabilities
- **Health Check** endpoint
- **Sample Data** pre-loaded

## API Endpoints

### Core Endpoints
- `GET /` - Welcome message and API info
- `GET /health` - Health check
- `GET /docs` - Interactive API documentation (Swagger UI)
- `GET /redoc` - Alternative API documentation

### Items Management
- `GET /items/` - Get all items (with optional filtering)
  - Query parameters: `category`, `min_price`, `max_price`, `limit`
- `GET /items/{item_id}` - Get specific item by ID
- `POST /items/` - Create new item
- `PUT /items/{item_id}` - Update existing item
- `DELETE /items/{item_id}` - Delete item

### Additional Features
- `GET /categories/` - Get all available categories
- `GET /search/?q={query}` - Search items by name, description, or category

## Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   uvicorn api.index:app --reload
   ```

3. **Access the API:**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

## Deployment to Vercel

### Prerequisites
- [Vercel CLI](https://vercel.com/cli) installed
- Vercel account

### Deploy Steps

1. **Install Vercel CLI (if not already installed):**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel:**
   ```bash
   vercel login
   ```

3. **Deploy the application:**
   ```bash
   vercel
   ```

4. **Follow the prompts:**
   - Set up and deploy? `Y`
   - Which scope? Choose your account
   - Link to existing project? `N` (for new project)
   - What's your project's name? `sample-fastapi-app` (or your preferred name)
   - In which directory is your code located? `./`

5. **Production deployment:**
   ```bash
   vercel --prod
   ```

### Environment Variables (if needed)
If your application requires environment variables, you can set them in the Vercel dashboard or using the CLI:

```bash
vercel env add VARIABLE_NAME
```

## Project Structure

```
.
├── api/
│   └── index.py          # Main FastAPI application
├── requirements.txt      # Python dependencies
├── vercel.json          # Vercel configuration
└── README.md            # This file
```

## Sample Data

The application comes pre-loaded with sample items:
- Laptop (Electronics)
- Coffee Mug (Kitchen)
- Python Book (Books)

## API Usage Examples

### Create a new item:
```bash
curl -X POST "https://your-app.vercel.app/items/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Wireless Mouse",
    "description": "Ergonomic wireless mouse",
    "price": 29.99,
    "category": "Electronics"
  }'
```

### Get items with filtering:
```bash
curl "https://your-app.vercel.app/items/?category=Electronics&min_price=20"
```

### Search items:
```bash
curl "https://your-app.vercel.app/search/?q=laptop"
```

## Technologies Used

- **FastAPI** - Modern, fast web framework for building APIs
- **Pydantic** - Data validation using Python type annotations
- **Uvicorn** - ASGI server implementation
- **Vercel** - Deployment platform

## License

This is a sample application for demonstration purposes.
