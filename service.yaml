# Cloud Run service configuration
# This file can be used with: gcloud run services replace service.yaml

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: fastapi-sample-app
  annotations:
    # Cloud Run specific annotations for free tier optimization
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Free tier optimized settings
        autoscaling.knative.dev/minScale: "0"  # Scale to zero for free tier
        autoscaling.knative.dev/maxScale: "10"  # Reasonable limit
        run.googleapis.com/cpu-throttling: "true"  # CPU throttling when idle
        run.googleapis.com/execution-environment: gen2
    spec:
      # Free tier resource limits
      containerConcurrency: 80  # Requests per container instance
      timeoutSeconds: 300  # 5 minutes timeout
      containers:
      - image: gcr.io/PROJECT_ID/fastapi-sample-app  # Replace PROJECT_ID
        ports:
        - containerPort: 8000
        env:
        - name: PORT
          value: "8000"
        resources:
          limits:
            # Free tier limits: 1 vCPU, 2GB memory
            cpu: "1000m"      # 1 vCPU
            memory: "512Mi"   # 512MB (well within 2GB free tier limit)
          requests:
            cpu: "100m"       # Minimum CPU request
            memory: "128Mi"   # Minimum memory request
        # Health checks for better reliability
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 60
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
